{"idf.port": "/dev/tty.usbmodem211101", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "/Users/<USER>/esp/tools/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clangd", "clangd.arguments": ["--background-index", "--query-driver=/Users/<USER>/esp/tools/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc", "--compile-commands-dir=/Users/<USER>/Projects/hello_world/build"], "idf.flashType": "UART"}