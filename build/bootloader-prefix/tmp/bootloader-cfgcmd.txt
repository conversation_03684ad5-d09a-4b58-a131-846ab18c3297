cmd='/opt/homebrew/bin/cmake;-DSDKCONFIG=/Users/<USER>/Projects/hello_world/sdkconfig;-DIDF_PATH=/Users/<USER>/esp/v5.4.1/esp-idf;-DIDF_TARGET=esp32s3;-DPYTHON_DEPS_CHECKED=1;-DPYTHON=/Users/<USER>/esp/tools/python_env/idf5.4_py3.9_env/bin/python;-DEXTRA_COMPONENT_DIRS=/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader;-DPROJECT_SOURCE_DIR=/Users/<USER>/Projects/hello_world;-DIGNORE_EXTRA_COMPONENT=;-GNinja;-S;<SOURCE_DIR><SOURCE_SUBDIR>;-B;<BINARY_DIR>'
