{"InstallScripts": ["/Users/<USER>/Projects/hello_world/build/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/xtensa/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_gpio/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_pm/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/mbedtls/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/mbedtls/include/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/mbedtls/3rdparty/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/mbedtls/3rdparty/everest/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/mbedtls/3rdparty/p256-m/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/mbedtls/library/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mbedtls/mbedtls/pkgconfig/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/bootloader/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esptool_py/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/partition_table/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_app_format/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_bootloader_format/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/app_update/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_partition/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/efuse/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/bootloader_support/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_mm/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/spi_flash/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_system/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_system/port/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_system/port/soc/esp32s3/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_common/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_rom/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/hal/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/log/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/heap/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/soc/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_security/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_hw_support/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_hw_support/port/esp32s3/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_hw_support/lowpower/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/freertos/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/newlib/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/newlib/port/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/pthread/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/cxx/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_timer/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_gptimer/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_ringbuf/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_uart/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/app_trace/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_event/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/nvs_flash/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_pcnt/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_spi/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_mcpwm/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_ana_cmpr/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_i2s/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/sdmmc/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_sdmmc/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_sdspi/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_sdio/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_dac/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_rmt/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_tsens/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_sdm/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_i2c/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_ledc/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_parlio/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_usb_serial_jtag/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/driver/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_phy/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_vfs_console/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/vfs/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/lwip/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_netif_stack/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_netif/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/wpa_supplicant/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_coex/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_wifi/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/bt/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/unity/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/cmock/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/console/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/http_parser/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp-tls/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_adc/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_isp/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_cam/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_jpeg/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_ppa/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_driver_touch_sens/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_eth/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_gdbstub/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_hid/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/tcp_transport/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_http_client/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_http_server/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_https_ota/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_https_server/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_psram/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_lcd/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/protobuf-c/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/protocomm/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/esp_local_ctrl/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/espcoredump/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/wear_levelling/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/fatfs/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/idf_test/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/ieee802154/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/json/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/mqtt/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/nvs_sec_provider/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/openthread/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/perfmon/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/rt/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/spiffs/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/touch_element/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/ulp/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/usb/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/wifi_provisioning/cmake_install.cmake", "/Users/<USER>/Projects/hello_world/build/esp-idf/main/cmake_install.cmake"], "Parallel": false}