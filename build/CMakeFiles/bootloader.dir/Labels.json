{"sources": [{"file": "/Users/<USER>/Projects/hello_world/build/CMakeFiles/bootloader"}, {"file": "/Users/<USER>/Projects/hello_world/build/CMakeFiles/bootloader.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/CMakeFiles/bootloader-complete.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}