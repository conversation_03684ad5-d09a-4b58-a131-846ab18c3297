# Target labels
 bootloader
# Source files and their labels
/Users/<USER>/Projects/hello_world/build/CMakeFiles/bootloader
/Users/<USER>/Projects/hello_world/build/CMakeFiles/bootloader.rule
/Users/<USER>/Projects/hello_world/build/CMakeFiles/bootloader-complete.rule
/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
/Users/<USER>/Projects/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
