# ninja log v6
4	43	1750302329374181104	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	3caffcd3968e15a2
2	54	1750302329372603599	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	b4a952a96d915210
2	55	1750302329372489057	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	78c858c647717d87
8	59	1750302329378899120	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	6c92ffa0b52fafd2
2	61	1750302329372700016	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	667c211a23ffd23c
10	63	1750302329380335625	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	b6f56b9fe4817eec
12	63	1750302329382145131	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	3773c3dce6dafbc0
2	64	1750302329372323723	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	278449f86ca685ea
7	65	1750302329377336073	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a5f4e0bf0996c60
12	65	1750302329382735132	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	f0b38dd421d55da4
6	72	1750302329376108694	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	e40729f37b715ae5
56	95	1750302329426032149	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	5814285880834039
44	105	1750302329414348153	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	b1842e7336347f49
63	114	1750302329433887008	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	b798eb390ff5673d
64	119	1750302329434934095	esp-idf/log/liblog.a	9e065e92c1cdfc5
54	123	1750302329424814979	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	aa7f85fdd30b2456
63	124	1750302329433674133	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	b944bf72352acfb3
65	134	1750302329435573931	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	8d93281103a509b4
61	140	1750302329431931585	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	1fc970c1ac2fe4c6
2	169	1750302329372789350	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	3d42c0b81d69bb26
60	171	1750302329429952412	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	da5dbaf1984a7d32
72	173	1750302329442883871	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	8056485097982463
123	174	1750302329493231870	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	39a14355df901ced
119	177	1750302329489632774	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	cf4bf05135c13148
124	178	1750302329494464415	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	d5e4ce0e3f1f41dd
134	193	1750302329504343406	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	ec2b733a69d16194
114	213	1750302329484473674	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	10577e0ac583328f
140	229	1750302329510935594	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	ebfd6693da082294
169	230	1750302329539122395	esp-idf/esp_rom/libesp_rom.a	287715fdfc80656e
174	239	1750302329544654997	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	705dc564db4f5a23
106	265	1750302329476257522	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	347f9dfefc80b83f
230	272	1750302329600869639	esp-idf/esp_common/libesp_common.a	ecb3ff80c43ebf80
229	279	1750302329599645510	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	1ac319f5365b167d
171	281	1750302329541460486	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	458533c2dca9c87d
239	282	1750302329609561209	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	2e9d16e4f0530dcb
173	296	1750302329543069325	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	4ee7fc5132619d1e
193	299	1750302329563469141	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	dd3fd0cfb3bc534d
95	311	1750302329465056444	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	b1186c5f96ef5e94
266	320	1750302329636048171	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	b6e01cd0e0664da
213	322	1750302329583244081	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	41cb7edc83e83de8
272	333	1750302329642129524	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	8f5753d52bc95624
282	333	1750302329652639976	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	78ffb85e4080236b
178	333	1750302329548602718	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	95a579ab4553d950
281	342	1750302329651774431	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	f23d66aa38fae803
333	380	1750302329703557726	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	6d24a8acb22b6a3a
177	381	1750302329547815548	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	6f40c6e039f50e4b
322	383	1750302329692631731	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	47d7a09b59de53b7
279	400	1750302329649502798	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	dea27ccc98eefdc
381	418	1750302329751374674	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	5ebdbace3a2fdad2
299	420	1750302329669580073	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	13fbc876efc6d462
342	423	1750302329712951840	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	ac606220dad4c83c
380	442	1750302329750917714	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	7acc53bd266c4325
383	444	1750302329753619223	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	5ea9f60187baff9a
333	453	1750302329703349183	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	653dc2f5186c2364
311	484	1750302329681806196	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	56c8315a475dd981
420	487	1750302329790616969	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	9fd67aa17b3f50fe
444	496	1750302329814726965	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	be1f0e2b8e274156
453	501	1750302329823264410	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	b4c0a7a53053f2c9
419	505	1750302329789033631	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	a31361698e38152d
400	510	1750302329770638112	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	c5c8d6954de4ce51
65	518	1750302329435809306	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	c5b250928f4cdc35
442	524	1750302329812556541	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	b7467da45bc1361f
423	526	1750302329793614313	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	9447942b35e82dc
485	549	1750302329855013222	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	d04efb1477aaa844
505	549	1750302329875007454	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	2b6373d0066bdce2
511	550	1750302329880952557	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	913ac4ea9169baca
524	557	1750302329894309976	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	71b6a39aa904713e
526	569	1750302329896901026	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	4e8f3c2f93a8cdbf
518	584	1750302329888133456	esp-idf/esp_hw_support/libesp_hw_support.a	470305b33cb63e7c
557	585	1750302329927091208	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	74d7972dc8339c8
550	586	1750302329920117561	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	498f0207cbbfef4d
549	586	1750302329918991682	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	4846b30662b42c4a
488	586	1750302329858005315	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	e481a575178c7cec
549	591	1750302329919732434	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	6ad2a0464e18b39d
496	593	1750302329866925094	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	ac3b1ec31c77aa1b
569	607	1750302329939839333	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	b0bdc2eda55bf6d6
587	616	1750302329956957140	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	333495c06b5c653e
586	620	1750302329956089970	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	b8a4649e687d0dd4
584	624	1750302329954877674	esp-idf/esp_system/libesp_system.a	6464df3e91afbaae
586	626	1750302329956600638	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	f0292f867b11f714
585	629	1750302329955353968	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	8678eb6b09694743
591	636	1750302329961752739	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	71fcfeba6aaf4625
593	638	1750302329963904912	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	719bec8223193b0
607	646	1750302329977567832	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	a278f1f075860a06
620	648	1750302329990432374	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	10f111a7a17e0d52
616	649	1750302329986721862	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	6f5174ff7ec2d052
629	664	1750302329999852280	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	5779ffc5a1dde64a
626	665	1750302329996727311	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	be33e69a5115dc29
638	669	1750302330008517267	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	d174931402b89e1f
624	671	1750302329994622721	esp-idf/efuse/libefuse.a	3cbcf051bc69b1a4
637	674	1750302330006966512	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	f9f3f8e8939eecd1
646	676	1750302330016015125	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	ae49d7701fbdc7a9
649	680	1750302330019130718	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	ab4b1dcdd148ec7
671	682	1750302330051717450	project_elf_src_esp32s3.c	94ab329ad86645f6
671	682	1750302330051717450	/Users/<USER>/Projects/hello_world/build/bootloader/project_elf_src_esp32s3.c	94ab329ad86645f6
648	691	1750302330018153507	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	47bb0a8edd1ad06e
665	697	1750302330035645106	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	28704f60320e3190
296	703	1750302329666688188	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	d908f6cfd23c27c0
664	705	1750302330034469185	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	4acab294bd78a9d9
682	708	1750302330052393911	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	5ab63c442ecee3de
669	714	1750302330039473202	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	73770a7cda7cca8
674	721	1750302330044148759	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	e4586ce4517d57cd
321	725	1750302329691014018	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	8683235dab46e168
333	748	1750302329703110849	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	34e0eaba8e0fa8ac
748	797	1750302330118178460	esp-idf/bootloader_support/libbootloader_support.a	a57ee681c57bac9a
797	820	1750302330167385204	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	a2cd13a852c5a193
820	842	1750302330189971695	esp-idf/spi_flash/libspi_flash.a	6533256532d66ab3
501	857	1750302329871194067	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	76a041dccafece
842	869	1750302330212045891	esp-idf/hal/libhal.a	d6d42ab5e7064ba1
869	892	1750302330239389231	esp-idf/micro-ecc/libmicro-ecc.a	55f3f3f2c5e69151
892	938	1750302330262247056	esp-idf/soc/libsoc.a	edfd4816142b453e
938	960	1750302330308171206	esp-idf/xtensa/libxtensa.a	fabce317ce6698ce
960	980	1750302330330194695	esp-idf/main/libmain.a	8e604b028c69cbd6
980	1026	1750302330350596387	bootloader.elf	5c6bf96fa229322e
1027	1110	1750302330479590477	.bin_timestamp	21fa2f36a66e3ef7
1027	1110	1750302330479590477	/Users/<USER>/Projects/hello_world/build/bootloader/.bin_timestamp	21fa2f36a66e3ef7
1110	1135	1750302330480086020	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	1ad6c021e591ecfb
1110	1135	1750302330480086020	/Users/<USER>/Projects/hello_world/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	1ad6c021e591ecfb
5	30	1750342284453120185	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	1ad6c021e591ecfb
5	30	1750342284453120185	/Users/<USER>/Projects/hello_world/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	1ad6c021e591ecfb
